#!/usr/bin/env python3
"""
Direct PDF hash calculator (backup method)
Calculates PDF hashes directly without using Edge Functions.
"""

import requests
import hashlib
import time
import argparse
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"

class DirectPDFHashCalculator:
    """Direct PDF hash calculator"""
    
    def __init__(self, supabase_key):
        """Initialize with Supabase key"""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = supabase_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {supabase_key}',
            'apikey': supabase_key
        }
        self.pdf_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'duplicates_found': 0
        }
        self.stats_lock = threading.Lock()
        self.hash_cache = {}  # Cache to track duplicates
    
    def compute_pdf_hash(self, pdf_url, max_retries=3):
        """Compute SHA-256 hash of PDF from URL"""
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    pdf_url,
                    headers=self.pdf_headers,
                    timeout=45,
                    stream=True
                )
                
                if not response.ok:
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    return None, f"HTTP {response.status_code}"
                
                # Compute hash incrementally
                hash_obj = hashlib.sha256()
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        hash_obj.update(chunk)
                
                pdf_hash = hash_obj.hexdigest()
                return pdf_hash, None
                
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                return None, str(e)
        
        return None, "Max retries exceeded"
    
    def update_record_hash(self, table_name, record_id, pdf_hash, is_duplicate):
        """Update record with calculated hash and duplicate status"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            
            update_data = {
                'pdf_hash': pdf_hash,
                'is_duplicate': is_duplicate,
                'duplicate_check_status': 'completed',
                'updated_at': datetime.utcnow().isoformat()
            }
            
            params = {'id': f'eq.{record_id}'}
            
            response = requests.patch(
                url,
                headers=self.headers,
                json=update_data,
                params=params,
                timeout=10
            )
            
            return response.ok
            
        except Exception as e:
            print(f"❌ Error updating record {record_id}: {e}")
            return False
    
    def process_record(self, record, table_name):
        """Process a single record"""
        try:
            record_id = record['id']
            
            # Get PDF URL
            if table_name == 'bse_corporate_announcements':
                pdf_url = record.get('attachmentfile')
            else:
                pdf_url = record.get('attachment_url')
            
            if not pdf_url or pdf_url.strip() == '':
                with self.stats_lock:
                    self.stats['total_processed'] += 1
                return {'success': False, 'reason': 'No PDF URL'}
            
            # Calculate hash
            pdf_hash, error = self.compute_pdf_hash(pdf_url)
            
            if not pdf_hash:
                with self.stats_lock:
                    self.stats['total_processed'] += 1
                    self.stats['failed'] += 1
                return {'success': False, 'reason': error}
            
            # Check for duplicates
            is_duplicate = False
            if pdf_hash in self.hash_cache:
                is_duplicate = True
                with self.stats_lock:
                    self.stats['duplicates_found'] += 1
            else:
                self.hash_cache[pdf_hash] = {
                    'table': table_name,
                    'record_id': record_id,
                    'created_at': record.get('created_at')
                }
            
            # Update database
            if self.update_record_hash(table_name, record_id, pdf_hash, is_duplicate):
                with self.stats_lock:
                    self.stats['total_processed'] += 1
                    self.stats['successful'] += 1
                return {
                    'success': True,
                    'pdf_hash': pdf_hash,
                    'is_duplicate': is_duplicate
                }
            else:
                with self.stats_lock:
                    self.stats['total_processed'] += 1
                    self.stats['failed'] += 1
                return {'success': False, 'reason': 'Database update failed'}
                
        except Exception as e:
            with self.stats_lock:
                self.stats['total_processed'] += 1
                self.stats['failed'] += 1
            return {'success': False, 'reason': str(e)}
    
    def get_records_needing_hashes(self, table_name, limit=None):
        """Get records that need hash calculation"""
        try:
            params = {
                'select': '*',
                'is.pdf_hash': 'null',
                'order': 'created_at.asc'
            }
            
            if table_name == 'bse_corporate_announcements':
                params['not.attachmentfile'] = 'is.null'
            else:
                params['not.attachment_url'] = 'is.null'
            
            if limit:
                params['limit'] = limit
            
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.ok:
                return response.json()
            else:
                print(f"❌ Failed to fetch records: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching records: {e}")
            return []
    
    def process_table(self, table_name, max_workers=3, batch_size=25):
        """Process all records in a table"""
        print(f"\n🚀 Processing {table_name} (Direct Method)...")
        print("=" * 60)
        
        records = self.get_records_needing_hashes(table_name)
        
        if not records:
            print(f"✅ No records need hash calculation in {table_name}")
            return
        
        print(f"📊 Processing {len(records)} records with {max_workers} workers")
        
        # Process in batches
        total_batches = (len(records) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(records))
            batch_records = records[start_idx:end_idx]
            
            print(f"\n📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_records)} records)")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_record = {
                    executor.submit(self.process_record, record, table_name): record
                    for record in batch_records
                }
                
                completed = 0
                for future in as_completed(future_to_record):
                    record = future_to_record[future]
                    result = future.result()
                    completed += 1
                    
                    if result['success']:
                        duplicate_flag = " (DUPLICATE)" if result['is_duplicate'] else ""
                        print(f"✅ {completed}/{len(batch_records)} - {record['id']}: {result['pdf_hash'][:16]}...{duplicate_flag}")
                    else:
                        print(f"❌ {completed}/{len(batch_records)} - {record['id']}: {result['reason']}")
            
            # Delay between batches
            if batch_num < total_batches - 1:
                time.sleep(1)
        
        print(f"\n✅ Completed {table_name}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Calculate PDF hashes directly (backup method)')
    parser.add_argument('--supabase-key', required=True, help='Supabase API key')
    parser.add_argument('--max-workers', type=int, default=3, help='Maximum concurrent workers')
    parser.add_argument('--batch-size', type=int, default=25, help='Batch size for processing')
    parser.add_argument('--table', choices=['bse_corporate_announcements', 'nse_corporate_announcements'], 
                       help='Process only specific table')
    
    args = parser.parse_args()
    
    calculator = DirectPDFHashCalculator(args.supabase_key)
    
    try:
        start_time = datetime.now()
        
        tables = [args.table] if args.table else ['bse_corporate_announcements', 'nse_corporate_announcements']
        
        for table_name in tables:
            calculator.process_table(table_name, args.max_workers, args.batch_size)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🏁 DIRECT HASH CALCULATION COMPLETED")
        print("=" * 60)
        print(f"⏱️ Duration: {duration}")
        print(f"📊 Statistics:")
        print(f"   Total processed: {calculator.stats['total_processed']}")
        print(f"   Successful: {calculator.stats['successful']}")
        print(f"   Failed: {calculator.stats['failed']}")
        print(f"   Duplicates found: {calculator.stats['duplicates_found']}")
        
        if calculator.stats['total_processed'] > 0:
            success_rate = (calculator.stats['successful'] / calculator.stats['total_processed']) * 100
            print(f"   Success rate: {success_rate:.2f}%")
        
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Usage:
# python scripts/direct_hash_calculator.py --supabase-key YOUR_KEY
# python scripts/direct_hash_calculator.py --supabase-key YOUR_KEY --max-workers 5 --batch-size 20

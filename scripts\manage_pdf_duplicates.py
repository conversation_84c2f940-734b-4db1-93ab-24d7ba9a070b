#!/usr/bin/env python3
"""
PDF Duplicate Detection Management Script
Provides utilities to manage the PDF duplicate detection system.
"""

import requests
import json
import argparse
import sys
import os
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"

class PDFDuplicateManager:
    """Manager for PDF duplicate detection operations"""
    
    def __init__(self, supabase_key):
        """Initialize with Supabase key"""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = supabase_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {supabase_key}'
        }
    
    def get_statistics(self):
        """Get comprehensive duplicate detection statistics"""
        try:
            url = f"{self.supabase_url}/functions/v1/get-duplicate-stats"
            response = requests.post(url, headers=self.headers, json={}, timeout=30)
            
            if response.ok:
                return response.json()
            else:
                print(f"❌ Failed to get statistics: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting statistics: {e}")
            return None
    
    def process_pending_duplicates(self, batch_size=50, max_concurrent=5, table_filter='both'):
        """Process pending duplicate detections"""
        try:
            url = f"{self.supabase_url}/functions/v1/process-pending-duplicates"
            payload = {
                'batch_size': batch_size,
                'max_concurrent': max_concurrent,
                'table_filter': table_filter
            }
            
            print(f"🔄 Processing pending duplicates (batch_size={batch_size}, max_concurrent={max_concurrent})...")
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=300)
            
            if response.ok:
                return response.json()
            else:
                print(f"❌ Failed to process pending duplicates: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error processing pending duplicates: {e}")
            return None
    
    def check_single_record(self, record_id, table_name, pdf_url):
        """Check a single record for duplicates"""
        try:
            url = f"{self.supabase_url}/functions/v1/pdf-duplicate-detector"
            payload = {
                'record_id': record_id,
                'table_name': table_name,
                'pdf_url': pdf_url
            }
            
            print(f"🔍 Checking record {table_name}:{record_id}...")
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=60)
            
            if response.ok:
                return response.json()
            else:
                print(f"❌ Failed to check record: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error checking record: {e}")
            return None
    
    def print_statistics(self, stats):
        """Print formatted statistics"""
        if not stats or not stats.get('success'):
            print("❌ No statistics available")
            return
        
        print("\n" + "="*80)
        print("📊 PDF DUPLICATE DETECTION STATISTICS")
        print("="*80)
        print(f"📅 Generated: {stats.get('timestamp', 'Unknown')}")
        
        # Table statistics
        tables = stats.get('tables', {})
        for table_name, table_stats in tables.items():
            print(f"\n📋 {table_name.replace('_', ' ').title()}:")
            print(f"   Total records: {table_stats.get('total_records', 0):,}")
            print(f"   Records with PDFs: {table_stats.get('records_with_pdfs', 0):,}")
            print(f"   Duplicate records: {table_stats.get('duplicate_records', 0):,}")
            print(f"   Unique PDFs: {table_stats.get('unique_pdfs', 0):,}")
            print(f"   Duplicate percentage: {table_stats.get('duplicate_percentage', 0):.2f}%")
            
            # Top duplicated PDFs
            top_duplicates = table_stats.get('top_duplicated_pdfs', [])
            if top_duplicates:
                print(f"   Top duplicated PDFs:")
                for i, dup in enumerate(top_duplicates[:3], 1):
                    print(f"     {i}. Hash: {dup['pdf_hash'][:16]}... ({dup['duplicate_count']} copies)")
        
        # Cross-table analysis
        cross_table = stats.get('cross_table_analysis', {})
        if cross_table:
            print(f"\n🔄 Cross-Table Analysis:")
            print(f"   Total unique PDFs: {cross_table.get('total_unique_pdfs', 0):,}")
            print(f"   PDFs in both tables: {cross_table.get('pdfs_in_both_tables', 0):,}")
            print(f"   BSE only PDFs: {cross_table.get('bse_only_pdfs', 0):,}")
            print(f"   NSE only PDFs: {cross_table.get('nse_only_pdfs', 0):,}")
        
        # Processing status
        processing = stats.get('processing_status', {})
        if processing:
            print(f"\n⚙️ Processing Status:")
            print(f"   Pending: {processing.get('pending', 0):,}")
            print(f"   Processing: {processing.get('processing', 0):,}")
            print(f"   Completed: {processing.get('completed', 0):,}")
            print(f"   Failed: {processing.get('failed', 0):,}")
        
        print("="*80)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Manage PDF duplicate detection system')
    parser.add_argument('--supabase-key', required=True, help='Supabase API key')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Statistics command
    stats_parser = subparsers.add_parser('stats', help='Show duplicate detection statistics')
    
    # Process command
    process_parser = subparsers.add_parser('process', help='Process pending duplicate detections')
    process_parser.add_argument('--batch-size', type=int, default=50, help='Batch size for processing')
    process_parser.add_argument('--max-concurrent', type=int, default=5, help='Maximum concurrent requests')
    process_parser.add_argument('--table-filter', choices=['both', 'bse_corporate_announcements', 'nse_corporate_announcements'], 
                               default='both', help='Filter by table')
    
    # Check command
    check_parser = subparsers.add_parser('check', help='Check a single record for duplicates')
    check_parser.add_argument('--record-id', required=True, help='Record ID to check')
    check_parser.add_argument('--table', required=True, choices=['bse_corporate_announcements', 'nse_corporate_announcements'], 
                             help='Table name')
    check_parser.add_argument('--pdf-url', required=True, help='PDF URL to check')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize manager
    manager = PDFDuplicateManager(args.supabase_key)
    
    try:
        if args.command == 'stats':
            print("📊 Fetching duplicate detection statistics...")
            stats = manager.get_statistics()
            if stats:
                manager.print_statistics(stats)
            
        elif args.command == 'process':
            print("🔄 Processing pending duplicate detections...")
            result = manager.process_pending_duplicates(
                batch_size=args.batch_size,
                max_concurrent=args.max_concurrent,
                table_filter=args.table_filter
            )
            
            if result and result.get('success'):
                print(f"\n✅ Processing completed!")
                print(f"📊 Total processed: {result.get('total_processed', 0)}")
                print(f"✅ Successful: {result.get('successful', 0)}")
                print(f"❌ Failed: {result.get('failed', 0)}")
                print(f"⏱️ Processing time: {result.get('processing_time_ms', 0)}ms")
                
                details = result.get('details', {})
                if details:
                    print(f"📋 BSE processed: {details.get('bse_processed', 0)}")
                    print(f"📋 NSE processed: {details.get('nse_processed', 0)}")
            else:
                print("❌ Processing failed")
                
        elif args.command == 'check':
            print(f"🔍 Checking record {args.table}:{args.record_id}...")
            result = manager.check_single_record(args.record_id, args.table, args.pdf_url)
            
            if result and result.get('success'):
                print(f"\n✅ Check completed!")
                print(f"🔍 PDF Hash: {result.get('pdf_hash', 'N/A')}")
                print(f"🔄 Is Duplicate: {result.get('is_duplicate', False)}")
                print(f"⏱️ Processing time: {result.get('processing_time_ms', 0)}ms")
            else:
                print("❌ Check failed")
                if result:
                    print(f"Error: {result.get('error', 'Unknown error')}")
                    
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Usage examples:
# python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY stats
# python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY process --batch-size 25 --max-concurrent 3
# python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY check --record-id "123" --table "bse_corporate_announcements" --pdf-url "https://example.com/file.pdf"

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface DuplicateCheckRequest {
  record_id: string
  table_name: string
  pdf_url: string
}

interface DuplicateCheckResponse {
  success: boolean
  pdf_hash?: string
  is_duplicate?: boolean
  error?: string
  processing_time_ms?: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const requestBody: DuplicateCheckRequest = await req.json()
    const { record_id, table_name, pdf_url } = requestBody

    console.log(`Processing duplicate check for ${table_name}:${record_id} - ${pdf_url}`)

    // Validate input
    if (!record_id || !table_name || !pdf_url) {
      throw new Error('Missing required fields: record_id, table_name, pdf_url')
    }

    if (!['bse_corporate_announcements', 'nse_corporate_announcements'].includes(table_name)) {
      throw new Error('Invalid table_name. Must be bse_corporate_announcements or nse_corporate_announcements')
    }

    // Update status to processing
    await updateProcessingStatus(supabaseClient, table_name, record_id, 'processing')

    // Step 1: Download and hash PDF
    const pdfHash = await computePdfHash(pdf_url)
    
    if (!pdfHash) {
      // Mark as failed and return
      await updateProcessingStatus(supabaseClient, table_name, record_id, 'failed')
      throw new Error('Failed to compute PDF hash')
    }

    console.log(`Computed PDF hash: ${pdfHash}`)

    // Step 2: Check for existing duplicates across both tables
    const isDuplicate = await checkForDuplicates(supabaseClient, pdfHash, table_name, record_id)

    console.log(`Duplicate check result: ${isDuplicate}`)

    // Step 3: Update the record with results
    await updateRecord(supabaseClient, table_name, record_id, pdfHash, isDuplicate)

    const processingTime = Date.now() - startTime

    const response: DuplicateCheckResponse = {
      success: true,
      pdf_hash: pdfHash,
      is_duplicate: isDuplicate,
      processing_time_ms: processingTime
    }

    console.log(`Duplicate check completed in ${processingTime}ms`)

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error in duplicate check:', error)
    
    const processingTime = Date.now() - startTime
    const errorResponse: DuplicateCheckResponse = {
      success: false,
      error: error.message,
      processing_time_ms: processingTime
    }

    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

async function computePdfHash(pdfUrl: string): Promise<string | null> {
  try {
    console.log(`Fetching PDF: ${pdfUrl}`)
    
    const response = await fetch(pdfUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/pdf,*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })

    if (!response.ok) {
      console.error(`HTTP ${response.status}: ${response.statusText} for ${pdfUrl}`)
      return null
    }

    console.log(`Successfully fetched PDF (${response.headers.get('content-length')} bytes)`)
    
    // Get PDF data as array buffer
    const pdfData = await response.arrayBuffer()
    
    // Compute SHA-256 hash
    const hashBuffer = await crypto.subtle.digest("SHA-256", pdfData)
    
    // Convert to hex string
    const hashArray = new Uint8Array(hashBuffer)
    const hashHex = Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')

    console.log(`Computed hash: ${hashHex}`)
    return hashHex

  } catch (error) {
    console.error(`Error computing PDF hash for ${pdfUrl}:`, error)
    return null
  }
}

async function checkForDuplicates(
  supabaseClient: any,
  pdfHash: string,
  currentTable: string,
  currentRecordId: string
): Promise<boolean> {
  try {
    // Check BSE table
    const { data: bseData, error: bseError } = await supabaseClient
      .from('bse_corporate_announcements')
      .select('id, created_at')
      .eq('pdf_hash', pdfHash)
      .neq('id', currentTable === 'bse_corporate_announcements' ? currentRecordId : 'never-match')

    if (bseError) {
      console.error('Error checking BSE duplicates:', bseError)
    }

    // Check NSE table
    const { data: nseData, error: nseError } = await supabaseClient
      .from('nse_corporate_announcements')
      .select('id, created_at')
      .eq('pdf_hash', pdfHash)
      .neq('id', currentTable === 'nse_corporate_announcements' ? currentRecordId : 'never-match')

    if (nseError) {
      console.error('Error checking NSE duplicates:', nseError)
    }

    const totalDuplicates = (bseData?.length || 0) + (nseData?.length || 0)
    
    console.log(`Found ${totalDuplicates} existing records with hash ${pdfHash}`)
    
    return totalDuplicates > 0

  } catch (error) {
    console.error('Error checking for duplicates:', error)
    return false // Default to not duplicate if check fails
  }
}

async function updateRecord(
  supabaseClient: any,
  tableName: string,
  recordId: string,
  pdfHash: string,
  isDuplicate: boolean
): Promise<void> {
  try {
    const updateData = {
      pdf_hash: pdfHash,
      is_duplicate: isDuplicate,
      duplicate_check_status: 'completed',
      updated_at: new Date().toISOString()
    }

    const { error } = await supabaseClient
      .from(tableName)
      .update(updateData)
      .eq('id', recordId)

    if (error) {
      throw new Error(`Failed to update record: ${error.message}`)
    }

    console.log(`Updated ${tableName}:${recordId} - hash: ${pdfHash}, duplicate: ${isDuplicate}`)

  } catch (error) {
    console.error('Error updating record:', error)
    throw error
  }
}

async function updateProcessingStatus(
  supabaseClient: any,
  tableName: string,
  recordId: string,
  status: string
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from(tableName)
      .update({ 
        duplicate_check_status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', recordId)

    if (error) {
      console.error(`Failed to update status to ${status}:`, error)
    }

  } catch (error) {
    console.error('Error updating processing status:', error)
  }
}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface DuplicateCheckRequest {
  record_id: string
  table_name: string
  pdf_url: string
}

interface DuplicateCheckResponse {
  success: boolean
  pdf_hash?: string
  is_duplicate?: boolean
  error?: string
  processing_time_ms?: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const requestBody: DuplicateCheckRequest = await req.json()
    const { record_id, table_name, pdf_url } = requestBody

    console.log(`[TRIGGER] Processing duplicate check for ${table_name}:${record_id}`)
    console.log(`[TRIGGER] PDF URL: ${pdf_url}`)

    // Validate input
    if (!record_id || !table_name || !pdf_url) {
      throw new Error('Missing required fields: record_id, table_name, pdf_url')
    }

    if (!['bse_corporate_announcements', 'nse_corporate_announcements'].includes(table_name)) {
      throw new Error('Invalid table_name. Must be bse_corporate_announcements or nse_corporate_announcements')
    }

    // Validate PDF URL format
    if (!isValidPdfUrl(pdf_url)) {
      console.log(`[TRIGGER] Invalid PDF URL format, skipping: ${pdf_url}`)
      await updateProcessingStatus(supabaseClient, table_name, record_id, 'completed')
      return new Response(
        JSON.stringify({
          success: true,
          pdf_hash: null,
          is_duplicate: false,
          processing_time_ms: Date.now() - startTime,
          message: 'Invalid PDF URL, skipped processing'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    // Update status to processing
    await updateProcessingStatus(supabaseClient, table_name, record_id, 'processing')

    // Step 1: Download and hash PDF
    const pdfHash = await computePdfHash(pdf_url)
    
    if (!pdfHash) {
      // Mark as failed and return
      await updateProcessingStatus(supabaseClient, table_name, record_id, 'failed')
      throw new Error('Failed to compute PDF hash')
    }

    console.log(`Computed PDF hash: ${pdfHash}`)

    // Step 2: Check for existing duplicates across both tables
    const isDuplicate = await checkForDuplicates(supabaseClient, pdfHash, table_name, record_id)

    console.log(`Duplicate check result: ${isDuplicate}`)

    // Step 3: Update the record with results
    await updateRecord(supabaseClient, table_name, record_id, pdfHash, isDuplicate)

    const processingTime = Date.now() - startTime

    const response: DuplicateCheckResponse = {
      success: true,
      pdf_hash: pdfHash,
      is_duplicate: isDuplicate,
      processing_time_ms: processingTime
    }

    console.log(`Duplicate check completed in ${processingTime}ms`)

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error in duplicate check:', error)
    
    const processingTime = Date.now() - startTime
    const errorResponse: DuplicateCheckResponse = {
      success: false,
      error: error.message,
      processing_time_ms: processingTime
    }

    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

function isValidPdfUrl(url: string): boolean {
  try {
    if (!url || typeof url !== 'string' || url.trim() === '') {
      return false
    }

    // Check for null-like values
    const nullValues = ['null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined']
    if (nullValues.includes(url.trim())) {
      return false
    }

    // Basic URL validation
    const urlObj = new URL(url)
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }

    // Check if it looks like a PDF URL
    const path = urlObj.pathname.toLowerCase()
    return path.includes('.pdf') || path.includes('pdf')

  } catch (error) {
    return false
  }
}

async function computePdfHash(pdfUrl: string): Promise<string | null> {
  const maxRetries = 3
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[HASH] Fetching PDF (attempt ${attempt}/${maxRetries}): ${pdfUrl}`)

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 45000) // 45 second timeout

      const response = await fetch(pdfUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/pdf,*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cache-Control': 'no-cache'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const contentType = response.headers.get('content-type')

      console.log(`[HASH] Successfully fetched PDF - Size: ${contentLength} bytes, Type: ${contentType}`)

      // Validate content type
      if (contentType && !contentType.includes('pdf') && !contentType.includes('application/octet-stream')) {
        console.warn(`[HASH] Unexpected content type: ${contentType}`)
      }

      // Get PDF data as array buffer
      const pdfData = await response.arrayBuffer()

      if (pdfData.byteLength === 0) {
        throw new Error('Empty PDF file')
      }

      console.log(`[HASH] Processing ${pdfData.byteLength} bytes for hashing`)

      // Compute SHA-256 hash
      const hashBuffer = await crypto.subtle.digest("SHA-256", pdfData)

      // Convert to hex string
      const hashArray = new Uint8Array(hashBuffer)
      const hashHex = Array.from(hashArray)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')

      console.log(`[HASH] Computed hash: ${hashHex}`)
      return hashHex

    } catch (error) {
      lastError = error as Error
      console.error(`[HASH] Attempt ${attempt} failed for ${pdfUrl}:`, error.message)

      if (attempt < maxRetries) {
        const delay = attempt * 2000 // Exponential backoff: 2s, 4s, 6s
        console.log(`[HASH] Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  console.error(`[HASH] All ${maxRetries} attempts failed for ${pdfUrl}. Last error:`, lastError?.message)
  return null
}

async function checkForDuplicates(
  supabaseClient: any,
  pdfHash: string,
  currentTable: string,
  currentRecordId: string
): Promise<boolean> {
  try {
    // Check BSE table for existing records with same hash
    const { data: bseData, error: bseError } = await supabaseClient
      .from('bse_corporate_announcements')
      .select('id, created_at, attachmentfile')
      .eq('pdf_hash', pdfHash)
      .neq('id', currentTable === 'bse_corporate_announcements' ? currentRecordId : 'never-match')

    if (bseError) {
      console.error('Error checking BSE duplicates:', bseError)
    }

    // Check NSE table for existing records with same hash
    const { data: nseData, error: nseError } = await supabaseClient
      .from('nse_corporate_announcements')
      .select('id, created_at, attachment_url')
      .eq('pdf_hash', pdfHash)
      .neq('id', currentTable === 'nse_corporate_announcements' ? currentRecordId : 'never-match')

    if (nseError) {
      console.error('Error checking NSE duplicates:', nseError)
    }

    const bseCount = bseData?.length || 0
    const nseCount = nseData?.length || 0
    const totalDuplicates = bseCount + nseCount

    console.log(`Found ${totalDuplicates} existing records with hash ${pdfHash} (BSE: ${bseCount}, NSE: ${nseCount})`)

    // If we found duplicates, also mark those existing records as duplicates
    // (except the first one chronologically)
    if (totalDuplicates > 0) {
      await markExistingDuplicates(supabaseClient, pdfHash, currentTable, currentRecordId)
    }

    return totalDuplicates > 0

  } catch (error) {
    console.error('Error checking for duplicates:', error)
    return false // Default to not duplicate if check fails
  }
}

async function markExistingDuplicates(
  supabaseClient: any,
  pdfHash: string,
  currentTable: string,
  currentRecordId: string
): Promise<void> {
  try {
    // Get all records with this hash from both tables, ordered by creation time
    const allRecords = []

    // Get BSE records
    const { data: bseData } = await supabaseClient
      .from('bse_corporate_announcements')
      .select('id, created_at')
      .eq('pdf_hash', pdfHash)
      .order('created_at', { ascending: true })

    if (bseData) {
      bseData.forEach(record => {
        allRecords.push({
          ...record,
          table: 'bse_corporate_announcements'
        })
      })
    }

    // Get NSE records
    const { data: nseData } = await supabaseClient
      .from('nse_corporate_announcements')
      .select('id, created_at')
      .eq('pdf_hash', pdfHash)
      .order('created_at', { ascending: true })

    if (nseData) {
      nseData.forEach(record => {
        allRecords.push({
          ...record,
          table: 'nse_corporate_announcements'
        })
      })
    }

    // Sort all records by creation time
    allRecords.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())

    // Mark all except the first one as duplicates
    for (let i = 1; i < allRecords.length; i++) {
      const record = allRecords[i]

      await supabaseClient
        .from(record.table)
        .update({
          is_duplicate: true,
          duplicate_check_status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', record.id)
    }

    // Ensure the first record is marked as NOT duplicate
    if (allRecords.length > 0) {
      const firstRecord = allRecords[0]
      await supabaseClient
        .from(firstRecord.table)
        .update({
          is_duplicate: false,
          duplicate_check_status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', firstRecord.id)
    }

    console.log(`Updated duplicate flags for ${allRecords.length} records with hash ${pdfHash}`)

  } catch (error) {
    console.error('Error marking existing duplicates:', error)
  }
}

async function updateRecord(
  supabaseClient: any,
  tableName: string,
  recordId: string,
  pdfHash: string,
  isDuplicate: boolean
): Promise<void> {
  try {
    const updateData = {
      pdf_hash: pdfHash,
      is_duplicate: isDuplicate,
      duplicate_check_status: 'completed',
      updated_at: new Date().toISOString()
    }

    const { error } = await supabaseClient
      .from(tableName)
      .update(updateData)
      .eq('id', recordId)

    if (error) {
      throw new Error(`Failed to update record: ${error.message}`)
    }

    console.log(`Updated ${tableName}:${recordId} - hash: ${pdfHash}, duplicate: ${isDuplicate}`)

  } catch (error) {
    console.error('Error updating record:', error)
    throw error
  }
}

async function updateProcessingStatus(
  supabaseClient: any,
  tableName: string,
  recordId: string,
  status: string
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from(tableName)
      .update({ 
        duplicate_check_status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', recordId)

    if (error) {
      console.error(`Failed to update status to ${status}:`, error)
    }

  } catch (error) {
    console.error('Error updating processing status:', error)
  }
}

#!/usr/bin/env python3
"""
Calculate PDF hashes for all existing records
This script processes all existing records and calculates PDF hashes using the Edge Function.
"""

import requests
import json
import time
import argparse
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"

class PDFHashCalculator:
    """Calculator for PDF hashes using Edge Functions"""
    
    def __init__(self, supabase_key):
        """Initialize with Supabase key"""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = supabase_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {supabase_key}',
            'apikey': supabase_key
        }
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        self.stats_lock = threading.Lock()
    
    def get_records_needing_hashes(self, table_name, limit=None):
        """Get records that need PDF hash calculation"""
        try:
            # Build query parameters
            params = {
                'select': '*',
                'is.pdf_hash': 'null'  # Records where pdf_hash is null
            }
            
            if table_name == 'bse_corporate_announcements':
                params['not.attachmentfile'] = 'is.null'  # Has attachment file
            elif table_name == 'nse_corporate_announcements':
                params['not.attachment_url'] = 'is.null'  # Has attachment URL
            
            if limit:
                params['limit'] = limit
            
            params['order'] = 'created_at.asc'  # Process oldest first
            
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            
            print(f"🔍 Fetching records from {table_name} that need hash calculation...")
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.ok:
                records = response.json()
                print(f"✅ Found {len(records)} records needing hash calculation in {table_name}")
                return records
            else:
                print(f"❌ Failed to fetch records from {table_name}: {response.status_code}")
                print(f"Response: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching records from {table_name}: {e}")
            return []
    
    def calculate_single_hash(self, record, table_name):
        """Calculate hash for a single record using Edge Function"""
        try:
            record_id = record['id']
            
            # Get PDF URL based on table
            if table_name == 'bse_corporate_announcements':
                pdf_url = record.get('attachmentfile')
            else:  # nse_corporate_announcements
                pdf_url = record.get('attachment_url')
            
            if not pdf_url or pdf_url.strip() == '':
                with self.stats_lock:
                    self.stats['skipped'] += 1
                return {'success': False, 'reason': 'No PDF URL'}
            
            # Call Edge Function
            edge_function_url = f"{self.supabase_url}/functions/v1/pdf-duplicate-detector"
            
            payload = {
                "record_id": str(record_id),
                "table_name": table_name,
                "pdf_url": pdf_url
            }
            
            response = requests.post(
                edge_function_url,
                headers=self.headers,
                json=payload,
                timeout=120  # 2 minutes timeout for PDF processing
            )
            
            if response.ok:
                result = response.json()
                if result.get('success'):
                    with self.stats_lock:
                        self.stats['successful'] += 1
                    return {
                        'success': True,
                        'record_id': record_id,
                        'pdf_hash': result.get('pdf_hash'),
                        'is_duplicate': result.get('is_duplicate'),
                        'processing_time': result.get('processing_time_ms')
                    }
                else:
                    with self.stats_lock:
                        self.stats['failed'] += 1
                    return {
                        'success': False,
                        'record_id': record_id,
                        'reason': result.get('error', 'Unknown error')
                    }
            else:
                with self.stats_lock:
                    self.stats['failed'] += 1
                return {
                    'success': False,
                    'record_id': record_id,
                    'reason': f'HTTP {response.status_code}: {response.text[:100]}'
                }
                
        except Exception as e:
            with self.stats_lock:
                self.stats['failed'] += 1
            return {
                'success': False,
                'record_id': record.get('id', 'unknown'),
                'reason': str(e)
            }
    
    def process_table(self, table_name, max_workers=3, batch_size=50):
        """Process all records in a table"""
        print(f"\n🚀 Processing {table_name}...")
        print("=" * 60)
        
        # Get all records needing hashes
        all_records = self.get_records_needing_hashes(table_name)
        
        if not all_records:
            print(f"✅ No records need hash calculation in {table_name}")
            return
        
        print(f"📊 Processing {len(all_records)} records with {max_workers} workers")
        
        # Process in batches to avoid overwhelming the system
        total_batches = (len(all_records) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(all_records))
            batch_records = all_records[start_idx:end_idx]
            
            print(f"\n📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_records)} records)")
            
            # Process batch with thread pool
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_record = {
                    executor.submit(self.calculate_single_hash, record, table_name): record
                    for record in batch_records
                }
                
                # Process completed tasks
                completed = 0
                for future in as_completed(future_to_record):
                    record = future_to_record[future]
                    result = future.result()
                    completed += 1
                    
                    with self.stats_lock:
                        self.stats['total_processed'] += 1
                    
                    if result['success']:
                        print(f"✅ {completed}/{len(batch_records)} - {record['id']}: {result['pdf_hash'][:16]}... (duplicate: {result['is_duplicate']})")
                    else:
                        print(f"❌ {completed}/{len(batch_records)} - {record['id']}: {result['reason']}")
                    
                    # Print progress every 10 records
                    if completed % 10 == 0:
                        with self.stats_lock:
                            print(f"📊 Progress: {self.stats['total_processed']} total, {self.stats['successful']} successful, {self.stats['failed']} failed")
            
            # Small delay between batches
            if batch_num < total_batches - 1:
                print("⏸️ Waiting 2 seconds before next batch...")
                time.sleep(2)
        
        print(f"\n✅ Completed processing {table_name}")
    
    def process_all_tables(self, max_workers=3, batch_size=50, tables=None):
        """Process all tables or specified tables"""
        start_time = datetime.now()
        
        if tables is None:
            tables = ['bse_corporate_announcements', 'nse_corporate_announcements']
        
        print("🚀 Starting PDF hash calculation for all existing records")
        print("=" * 80)
        print(f"📅 Started at: {start_time}")
        print(f"📋 Tables to process: {', '.join(tables)}")
        print(f"⚙️ Max workers: {max_workers}")
        print(f"📦 Batch size: {batch_size}")
        print("=" * 80)
        
        for table_name in tables:
            self.process_table(table_name, max_workers, batch_size)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 80)
        print("🏁 PDF HASH CALCULATION COMPLETED")
        print("=" * 80)
        print(f"📅 Completed at: {end_time}")
        print(f"⏱️ Total duration: {duration}")
        print(f"📊 Final Statistics:")
        print(f"   Total processed: {self.stats['total_processed']}")
        print(f"   Successful: {self.stats['successful']}")
        print(f"   Failed: {self.stats['failed']}")
        print(f"   Skipped: {self.stats['skipped']}")
        
        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_processed']) * 100
            print(f"   Success rate: {success_rate:.2f}%")
        
        print("=" * 80)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Calculate PDF hashes for all existing records')
    parser.add_argument('--supabase-key', required=True, help='Supabase API key (service role key recommended)')
    parser.add_argument('--max-workers', type=int, default=3, help='Maximum concurrent workers (default: 3)')
    parser.add_argument('--batch-size', type=int, default=50, help='Batch size for processing (default: 50)')
    parser.add_argument('--table', choices=['bse_corporate_announcements', 'nse_corporate_announcements'], 
                       help='Process only specific table (default: both)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without actually processing')
    
    args = parser.parse_args()
    
    # Initialize calculator
    calculator = PDFHashCalculator(args.supabase_key)
    
    try:
        if args.dry_run:
            print("🔍 DRY RUN MODE - Checking what would be processed...")
            tables = [args.table] if args.table else ['bse_corporate_announcements', 'nse_corporate_announcements']
            
            for table_name in tables:
                records = calculator.get_records_needing_hashes(table_name)
                print(f"📋 {table_name}: {len(records)} records would be processed")
        else:
            # Process tables
            tables = [args.table] if args.table else None
            calculator.process_all_tables(
                max_workers=args.max_workers,
                batch_size=args.batch_size,
                tables=tables
            )
            
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
        print(f"📊 Partial statistics: {calculator.stats}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Usage examples:
# python scripts/calculate_all_pdf_hashes.py --supabase-key YOUR_SERVICE_ROLE_KEY
# python scripts/calculate_all_pdf_hashes.py --supabase-key YOUR_KEY --max-workers 5 --batch-size 25
# python scripts/calculate_all_pdf_hashes.py --supabase-key YOUR_KEY --table bse_corporate_announcements
# python scripts/calculate_all_pdf_hashes.py --supabase-key YOUR_KEY --dry-run

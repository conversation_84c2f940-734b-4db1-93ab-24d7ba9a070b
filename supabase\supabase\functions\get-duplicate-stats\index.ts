import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface DuplicateStats {
  success: boolean
  timestamp: string
  tables: {
    bse_corporate_announcements: TableStats
    nse_corporate_announcements: TableStats
  }
  cross_table_analysis: CrossTableStats
  processing_status: ProcessingStatusStats
  error?: string
}

interface TableStats {
  total_records: number
  records_with_pdfs: number
  duplicate_records: number
  unique_pdfs: number
  duplicate_percentage: number
  top_duplicated_pdfs: Array<{
    pdf_hash: string
    duplicate_count: number
    sample_urls: string[]
  }>
}

interface CrossTableStats {
  total_unique_pdfs: number
  pdfs_in_both_tables: number
  bse_only_pdfs: number
  nse_only_pdfs: number
}

interface ProcessingStatusStats {
  pending: number
  processing: number
  completed: number
  failed: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Generating duplicate detection statistics...')

    // Get BSE table statistics
    const bseStats = await getTableStats(supabaseClient, 'bse_corporate_announcements', 'attachmentfile')
    
    // Get NSE table statistics
    const nseStats = await getTableStats(supabaseClient, 'nse_corporate_announcements', 'attachment_url')
    
    // Get cross-table analysis
    const crossTableStats = await getCrossTableStats(supabaseClient)
    
    // Get processing status statistics
    const processingStats = await getProcessingStatusStats(supabaseClient)

    const response: DuplicateStats = {
      success: true,
      timestamp: new Date().toISOString(),
      tables: {
        bse_corporate_announcements: bseStats,
        nse_corporate_announcements: nseStats
      },
      cross_table_analysis: crossTableStats,
      processing_status: processingStats
    }

    console.log('Statistics generated successfully')

    return new Response(
      JSON.stringify(response, null, 2),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error generating statistics:', error)
    
    const errorResponse: DuplicateStats = {
      success: false,
      timestamp: new Date().toISOString(),
      tables: {
        bse_corporate_announcements: getEmptyTableStats(),
        nse_corporate_announcements: getEmptyTableStats()
      },
      cross_table_analysis: getEmptyCrossTableStats(),
      processing_status: getEmptyProcessingStats(),
      error: error.message
    }

    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

async function getTableStats(
  supabaseClient: any,
  tableName: string,
  pdfUrlColumn: string
): Promise<TableStats> {
  try {
    // Get basic counts
    const { data: basicStats, error: basicError } = await supabaseClient
      .from(tableName)
      .select('id, is_duplicate, pdf_hash, ' + pdfUrlColumn, { count: 'exact' })

    if (basicError) {
      throw new Error(`Error getting basic stats for ${tableName}: ${basicError.message}`)
    }

    const totalRecords = basicStats?.length || 0
    const recordsWithPdfs = basicStats?.filter(r => r[pdfUrlColumn] && r[pdfUrlColumn].trim() !== '').length || 0
    const duplicateRecords = basicStats?.filter(r => r.is_duplicate === true).length || 0
    
    // Count unique PDFs by hash
    const uniquePdfHashes = new Set(
      basicStats?.filter(r => r.pdf_hash).map(r => r.pdf_hash) || []
    )
    const uniquePdfs = uniquePdfHashes.size

    const duplicatePercentage = recordsWithPdfs > 0 ? (duplicateRecords / recordsWithPdfs) * 100 : 0

    // Get top duplicated PDFs
    const topDuplicatedPdfs = await getTopDuplicatedPdfs(supabaseClient, tableName, pdfUrlColumn)

    return {
      total_records: totalRecords,
      records_with_pdfs: recordsWithPdfs,
      duplicate_records: duplicateRecords,
      unique_pdfs: uniquePdfs,
      duplicate_percentage: Math.round(duplicatePercentage * 100) / 100,
      top_duplicated_pdfs: topDuplicatedPdfs
    }

  } catch (error) {
    console.error(`Error getting stats for ${tableName}:`, error)
    return getEmptyTableStats()
  }
}

async function getTopDuplicatedPdfs(
  supabaseClient: any,
  tableName: string,
  pdfUrlColumn: string
): Promise<Array<{pdf_hash: string, duplicate_count: number, sample_urls: string[]}>> {
  try {
    // Get PDF hash counts
    const { data: hashCounts, error } = await supabaseClient
      .from(tableName)
      .select(`pdf_hash, ${pdfUrlColumn}`)
      .not('pdf_hash', 'is', null)

    if (error || !hashCounts) {
      return []
    }

    // Group by hash and count
    const hashGroups = new Map<string, string[]>()
    
    hashCounts.forEach(record => {
      const hash = record.pdf_hash
      const url = record[pdfUrlColumn]
      
      if (hash && url) {
        if (!hashGroups.has(hash)) {
          hashGroups.set(hash, [])
        }
        hashGroups.get(hash)!.push(url)
      }
    })

    // Convert to array and sort by count
    const duplicatedHashes = Array.from(hashGroups.entries())
      .filter(([hash, urls]) => urls.length > 1)
      .map(([hash, urls]) => ({
        pdf_hash: hash,
        duplicate_count: urls.length,
        sample_urls: urls.slice(0, 3) // First 3 URLs as samples
      }))
      .sort((a, b) => b.duplicate_count - a.duplicate_count)
      .slice(0, 5) // Top 5

    return duplicatedHashes

  } catch (error) {
    console.error('Error getting top duplicated PDFs:', error)
    return []
  }
}

async function getCrossTableStats(supabaseClient: any): Promise<CrossTableStats> {
  try {
    // Get all PDF hashes from both tables
    const { data: bseHashes, error: bseError } = await supabaseClient
      .from('bse_corporate_announcements')
      .select('pdf_hash')
      .not('pdf_hash', 'is', null)

    const { data: nseHashes, error: nseError } = await supabaseClient
      .from('nse_corporate_announcements')
      .select('pdf_hash')
      .not('pdf_hash', 'is', null)

    if (bseError || nseError) {
      throw new Error('Error getting cross-table data')
    }

    const bseHashSet = new Set((bseHashes || []).map(r => r.pdf_hash))
    const nseHashSet = new Set((nseHashes || []).map(r => r.pdf_hash))
    
    const allHashes = new Set([...bseHashSet, ...nseHashSet])
    const commonHashes = new Set([...bseHashSet].filter(hash => nseHashSet.has(hash)))
    
    return {
      total_unique_pdfs: allHashes.size,
      pdfs_in_both_tables: commonHashes.size,
      bse_only_pdfs: bseHashSet.size - commonHashes.size,
      nse_only_pdfs: nseHashSet.size - commonHashes.size
    }

  } catch (error) {
    console.error('Error getting cross-table stats:', error)
    return getEmptyCrossTableStats()
  }
}

async function getProcessingStatusStats(supabaseClient: any): Promise<ProcessingStatusStats> {
  try {
    // Get status counts from both tables
    const { data: bseStatus, error: bseError } = await supabaseClient
      .from('bse_corporate_announcements')
      .select('duplicate_check_status')

    const { data: nseStatus, error: nseError } = await supabaseClient
      .from('nse_corporate_announcements')
      .select('duplicate_check_status')

    if (bseError || nseError) {
      throw new Error('Error getting processing status')
    }

    const allStatuses = [
      ...(bseStatus || []).map(r => r.duplicate_check_status),
      ...(nseStatus || []).map(r => r.duplicate_check_status)
    ]

    const statusCounts = {
      pending: allStatuses.filter(s => s === 'pending').length,
      processing: allStatuses.filter(s => s === 'processing').length,
      completed: allStatuses.filter(s => s === 'completed').length,
      failed: allStatuses.filter(s => s === 'failed').length
    }

    return statusCounts

  } catch (error) {
    console.error('Error getting processing status stats:', error)
    return getEmptyProcessingStats()
  }
}

function getEmptyTableStats(): TableStats {
  return {
    total_records: 0,
    records_with_pdfs: 0,
    duplicate_records: 0,
    unique_pdfs: 0,
    duplicate_percentage: 0,
    top_duplicated_pdfs: []
  }
}

function getEmptyCrossTableStats(): CrossTableStats {
  return {
    total_unique_pdfs: 0,
    pdfs_in_both_tables: 0,
    bse_only_pdfs: 0,
    nse_only_pdfs: 0
  }
}

function getEmptyProcessingStats(): ProcessingStatusStats {
  return {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  }
}
